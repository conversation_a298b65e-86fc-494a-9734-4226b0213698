import { createApp, App } from 'vue';
import USpaceDetailTooltip from '/@/views/scene/components/USpaceDetailTooltip.vue';
import type { CabinetDeviceInfo } from '/@/api/asset/cabinet';

/**
 * U位详情弹窗数据接口
 */
export interface USpaceTooltipData {
  cabinetId: number;
  cabinetName: string;
  uPosition: number;
  status: 'occupied' | 'available' | 'reserved' | 'unknown';
  device?: CabinetDeviceInfo;
}

/**
 * U位详情弹窗管理器
 * 负责显示和管理机柜U位的详情弹窗
 */
export class USpaceTooltipManager {
  private static instance: USpaceTooltipManager | null = null;
  private app: App | null = null;
  private container: HTMLElement | null = null;
  private isVisible: boolean = false;

  constructor() {
    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): USpaceTooltipManager {
    if (!USpaceTooltipManager.instance) {
      USpaceTooltipManager.instance = new USpaceTooltipManager();
    }
    return USpaceTooltipManager.instance;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听U位点击事件
    window.addEventListener('uspace-clicked', this.handleUSpaceClick.bind(this) as EventListener);
    
    // 监听点击其他区域关闭弹窗
    document.addEventListener('click', this.handleDocumentClick.bind(this));
  }

  /**
   * 处理U位点击事件
   */
  private handleUSpaceClick(event: CustomEvent): void {
    const { cabinetId, uPosition, status, device } = event.detail;
    
    console.log('[USpaceTooltipManager] U位点击事件:', event.detail);

    // 获取机柜名称（这里可以根据实际需求从API获取或缓存中获取）
    const cabinetName = `机柜-${cabinetId}`;

    // 计算弹窗位置（基于鼠标位置）
    const mouseX = (event as any).clientX || window.innerWidth / 2;
    const mouseY = (event as any).clientY || window.innerHeight / 2;

    const tooltipData: USpaceTooltipData = {
      cabinetId,
      cabinetName,
      uPosition,
      status,
      device
    };

    this.showTooltip(tooltipData, { x: mouseX + 10, y: mouseY + 10 });
  }

  /**
   * 处理文档点击事件（用于关闭弹窗）
   */
  private handleDocumentClick(event: MouseEvent): void {
    if (this.isVisible && this.container) {
      const target = event.target as HTMLElement;
      
      // 如果点击的不是弹窗内部，则关闭弹窗
      if (!this.container.contains(target)) {
        this.hideTooltip();
      }
    }
  }

  /**
   * 显示U位详情弹窗
   */
  public showTooltip(data: USpaceTooltipData, position: { x: number; y: number }): void {
    // 如果已经有弹窗显示，先关闭
    if (this.isVisible) {
      this.hideTooltip();
    }

    // 创建容器元素
    this.container = document.createElement('div');
    this.container.style.position = 'fixed';
    this.container.style.top = '0';
    this.container.style.left = '0';
    this.container.style.width = '100%';
    this.container.style.height = '100%';
    this.container.style.pointerEvents = 'none';
    this.container.style.zIndex = '9999';
    
    document.body.appendChild(this.container);

    // 调整弹窗位置，确保不超出屏幕边界
    const adjustedPosition = this.adjustPosition(position);

    // 创建Vue应用实例
    this.app = createApp(USpaceDetailTooltip, {
      visible: true,
      position: adjustedPosition,
      cabinetId: data.cabinetId,
      cabinetName: data.cabinetName,
      uPosition: data.uPosition,
      status: data.status,
      device: data.device,
      onClose: () => {
        this.hideTooltip();
      },
      onViewDevice: (device: CabinetDeviceInfo) => {
        this.handleViewDevice(device);
      },
      onUnbindDevice: (device: CabinetDeviceInfo) => {
        this.handleUnbindDevice(device);
      }
    });

    this.app.mount(this.container);
    this.isVisible = true;

    console.log('[USpaceTooltipManager] U位详情弹窗已显示');
  }

  /**
   * 隐藏U位详情弹窗
   */
  public hideTooltip(): void {
    if (this.app && this.container) {
      this.app.unmount();
      document.body.removeChild(this.container);
      
      this.app = null;
      this.container = null;
      this.isVisible = false;

      console.log('[USpaceTooltipManager] U位详情弹窗已隐藏');
    }
  }

  /**
   * 调整弹窗位置，确保不超出屏幕边界
   */
  private adjustPosition(position: { x: number; y: number }): { x: number; y: number } {
    const tooltipWidth = 300; // 估算弹窗宽度
    const tooltipHeight = 400; // 估算弹窗高度
    const margin = 20; // 边距

    let { x, y } = position;

    // 调整X坐标
    if (x + tooltipWidth + margin > window.innerWidth) {
      x = window.innerWidth - tooltipWidth - margin;
    }
    if (x < margin) {
      x = margin;
    }

    // 调整Y坐标
    if (y + tooltipHeight + margin > window.innerHeight) {
      y = window.innerHeight - tooltipHeight - margin;
    }
    if (y < margin) {
      y = margin;
    }

    return { x, y };
  }

  /**
   * 处理查看设备详情
   */
  private handleViewDevice(device: CabinetDeviceInfo): void {
    console.log('[USpaceTooltipManager] 查看设备详情:', device);
    
    // 触发自定义事件，通知其他组件
    window.dispatchEvent(new CustomEvent('view-device-detail', {
      detail: { device }
    }));

    // 关闭弹窗
    this.hideTooltip();
  }

  /**
   * 处理解绑设备
   */
  private handleUnbindDevice(device: CabinetDeviceInfo): void {
    console.log('[USpaceTooltipManager] 解绑设备:', device);
    
    // 触发自定义事件，通知其他组件
    window.dispatchEvent(new CustomEvent('unbind-device', {
      detail: { device }
    }));

    // 关闭弹窗
    this.hideTooltip();
  }

  /**
   * 检查弹窗是否可见
   */
  public isTooltipVisible(): boolean {
    return this.isVisible;
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 移除事件监听器
    window.removeEventListener('uspace-clicked', this.handleUSpaceClick.bind(this) as EventListener);
    document.removeEventListener('click', this.handleDocumentClick.bind(this));

    // 隐藏弹窗
    this.hideTooltip();

    // 清理单例
    USpaceTooltipManager.instance = null;

    console.log('[USpaceTooltipManager] 管理器已销毁');
  }
}
