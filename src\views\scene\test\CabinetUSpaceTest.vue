<template>
  <div class="w-full h-full bg-gray-900 p-4">
    <div class="mb-4">
      <h2 class="text-white text-xl mb-2">机柜U位拆分可视化测试</h2>
      <div class="flex gap-4">
        <button
          @click="activateUSpaceVisualization"
          :disabled="isActive"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-600"
        >
          激活U位拆分
        </button>
        <button
          @click="deactivateUSpaceVisualization"
          :disabled="!isActive"
          class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-600"
        >
          停用U位拆分
        </button>
        <button
          @click="testUSpaceClick"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          测试U位点击
        </button>
      </div>
    </div>

    <div class="bg-gray-800 p-4 rounded">
      <h3 class="text-white text-lg mb-2">状态信息</h3>
      <div class="text-gray-300 space-y-1">
        <div>可视化状态: <span :class="isActive ? 'text-green-400' : 'text-red-400'">{{ isActive ? '已激活' : '未激活' }}</span></div>
        <div>测试机柜ID: <span class="text-blue-400">{{ testCabinetId }}</span></div>
        <div>测试U位: <span class="text-blue-400">{{ testUPosition }}</span></div>
      </div>
    </div>

    <div class="mt-4 bg-gray-800 p-4 rounded">
      <h3 class="text-white text-lg mb-2">使用说明</h3>
      <div class="text-gray-300 space-y-2 text-sm">
        <p>1. 点击"激活U位拆分"按钮启用机柜U位可视化功能</p>
        <p>2. 在3D场景中，机柜将被拆分为独立的U位格子</p>
        <p>3. 不同颜色代表不同状态：</p>
        <ul class="ml-4 space-y-1">
          <li><span class="text-red-400">红色</span> - 已占用</li>
          <li><span class="text-green-400">绿色</span> - 空闲</li>
          <li><span class="text-yellow-400">黄色</span> - 预留</li>
          <li><span class="text-gray-400">灰色</span> - 未知</li>
        </ul>
        <p>4. 点击U位格子可查看详细信息</p>
        <p>5. 点击"测试U位点击"可模拟点击事件</p>
      </div>
    </div>

    <div class="mt-4 bg-gray-800 p-4 rounded">
      <h3 class="text-white text-lg mb-2">事件日志</h3>
      <div class="bg-black p-2 rounded h-32 overflow-y-auto">
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="text-xs text-gray-300 mb-1"
        >
          <span class="text-gray-500">[{{ log.timestamp }}]</span>
          <span :class="getLogColor(log.type)">{{ log.message }}</span>
        </div>
      </div>
      <button
        @click="clearLogs"
        class="mt-2 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
      >
        清空日志
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { HeatmapManager } from '/@/views/scene/lib/effects/HeatmapManager';
  import { GlobalHeatmapStateManager } from '/@/views/scene/lib/effects/GlobalHeatmapStateManager';

  interface EventLog {
    timestamp: string;
    type: 'info' | 'success' | 'error' | 'warning';
    message: string;
  }

  const isActive = ref(false);
  const testCabinetId = ref(1);
  const testUPosition = ref(5);
  const eventLogs = ref<EventLog[]>([]);

  const heatmapManager = HeatmapManager.getInstance();
  const globalHeatmapState = GlobalHeatmapStateManager.getInstance();

  /**
   * 添加事件日志
   */
  const addLog = (type: EventLog['type'], message: string): void => {
    const timestamp = new Date().toLocaleTimeString();
    eventLogs.value.unshift({ timestamp, type, message });
    
    // 限制日志数量
    if (eventLogs.value.length > 50) {
      eventLogs.value = eventLogs.value.slice(0, 50);
    }
  };

  /**
   * 获取日志颜色
   */
  const getLogColor = (type: EventLog['type']): string => {
    switch (type) {
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'warning':
        return 'text-yellow-400';
      case 'info':
      default:
        return 'text-blue-400';
    }
  };

  /**
   * 清空日志
   */
  const clearLogs = (): void => {
    eventLogs.value = [];
    addLog('info', '日志已清空');
  };

  /**
   * 激活U位可视化
   */
  const activateUSpaceVisualization = async (): Promise<void> => {
    try {
      addLog('info', '正在激活机柜U位拆分可视化...');
      
      const success = await globalHeatmapState.activateFunction('cabinet_u_space');
      
      if (success) {
        isActive.value = true;
        addLog('success', '机柜U位拆分可视化已激活');
      } else {
        addLog('error', '激活机柜U位拆分可视化失败');
      }
    } catch (error) {
      console.error('激活U位可视化失败:', error);
      addLog('error', `激活失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  /**
   * 停用U位可视化
   */
  const deactivateUSpaceVisualization = async (): Promise<void> => {
    try {
      addLog('info', '正在停用机柜U位拆分可视化...');
      
      await globalHeatmapState.clearAllFunctions();
      
      isActive.value = false;
      addLog('success', '机柜U位拆分可视化已停用');
    } catch (error) {
      console.error('停用U位可视化失败:', error);
      addLog('error', `停用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  /**
   * 测试U位点击事件
   */
  const testUSpaceClick = (): void => {
    addLog('info', `模拟点击机柜${testCabinetId.value}的U位${testUPosition.value}`);
    
    // 模拟U位点击事件
    const mockDevice = {
      id: 1,
      deviceId: 'DEV001',
      cabinetId: testCabinetId.value,
      startU: testUPosition.value,
      endU: testUPosition.value + 1,
      deviceName: '测试服务器',
      deviceType: '服务器',
      models: 'Dell PowerEdge R740',
      uSize: 2
    };

    window.dispatchEvent(new CustomEvent('uspace-clicked', {
      detail: {
        cabinetId: testCabinetId.value,
        uPosition: testUPosition.value,
        status: 'occupied',
        device: mockDevice
      }
    }));

    addLog('success', 'U位点击事件已触发');
  };

  /**
   * 处理U位点击事件
   */
  const handleUSpaceClick = (event: CustomEvent): void => {
    const { cabinetId, uPosition, status } = event.detail;
    addLog('info', `U位点击: 机柜${cabinetId}, U位${uPosition}, 状态${status}`);
  };

  /**
   * 处理查看设备详情事件
   */
  const handleViewDeviceDetail = (event: CustomEvent): void => {
    const { device } = event.detail;
    addLog('info', `查看设备详情: ${device.deviceName}`);
  };

  /**
   * 处理解绑设备事件
   */
  const handleUnbindDevice = (event: CustomEvent): void => {
    const { device } = event.detail;
    addLog('warning', `解绑设备: ${device.deviceName}`);
  };

  /**
   * 组件挂载
   */
  onMounted(() => {
    addLog('info', '机柜U位拆分测试组件已加载');
    
    // 监听事件
    window.addEventListener('uspace-clicked', handleUSpaceClick as EventListener);
    window.addEventListener('view-device-detail', handleViewDeviceDetail as EventListener);
    window.addEventListener('unbind-device', handleUnbindDevice as EventListener);
    
    // 检查当前状态
    const currentFunction = globalHeatmapState.getActiveFunction();
    if (currentFunction === 'cabinet_u_space') {
      isActive.value = true;
      addLog('info', '检测到U位拆分可视化已激活');
    }
  });

  /**
   * 组件卸载
   */
  onUnmounted(() => {
    // 移除事件监听器
    window.removeEventListener('uspace-clicked', handleUSpaceClick as EventListener);
    window.removeEventListener('view-device-detail', handleViewDeviceDetail as EventListener);
    window.removeEventListener('unbind-device', handleUnbindDevice as EventListener);
    
    addLog('info', '机柜U位拆分测试组件已卸载');
  });
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #374151;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
