import { Ref, ref } from 'vue';
import { SceneManager } from './SceneManager';
import { CameraController } from './CameraController';
import { LightingManager } from './LightingManager';
import { RenderingPipeline } from './RenderingPipeline';
import { AnimationLoop } from './AnimationLoop';
import { ControlManager } from './control/ControlManager';
import { Screensaver } from './Screensaver';
import { ModelLoaderManager } from './load/ModelLoaderManager';
import { ObjectSelection } from './selection/ObjectSelection';
import { ObjectDoubleClickHandler } from './selection/ObjectDoubleClickHandler';
import { useGlobalThreeStore } from '../store/globalThreeStore';
import { GridFloorManager } from './effects/GridFloorManager';
import { WeatherManager } from './WeatherManager';
import { SkyManager } from './SkyManager';
import { WeatherType } from '../types/weather';
import { HeatmapManager } from './effects/HeatmapManager';
import { USpaceTooltipManager } from './ui/USpaceTooltipManager';

export interface BuildingFloor {
  id: string;
  name?: string;
  modelPath?: string;
  [key: string]: any;
}

export interface BuildingData {
  id?: string;
  name?: string;
  modelPath: string;
  parkModelPath: string;
  floors?: BuildingFloor[];
  [key: string]: any;
}

export class SceneController {
  private initialized: boolean;
  private container: HTMLElement | null;
  private containerRef: Ref<HTMLElement | null>;
  private handlers: {
    stopScreensaver: (event: MouseEvent | TouchEvent) => void;
    handleClick: (event: MouseEvent) => void;
  };

  private sceneManager: SceneManager | null;
  private cameraController: CameraController | null;
  private lightingManager: LightingManager | null;
  private pipeline: RenderingPipeline | null;
  private controlManager: ControlManager | null;
  private animationLoop: AnimationLoop | null;
  private modelLoaderManager: ModelLoaderManager | null;
  private screensaver: Screensaver | null;
  private objectSelection: ObjectSelection | null;
  private objectDoubleClickHandler: ObjectDoubleClickHandler | null;
  private uSpaceTooltipManager: USpaceTooltipManager | null;

  private static _instance: SceneController | null = null;

  constructor() {
    this.initialized = false;
    this.container = null;
    this.containerRef = ref(null);
    this.handlers = {
      stopScreensaver: this._stopScreensaver.bind(this),
      handleClick: this._handleClick.bind(this),
    };

    this.sceneManager = null;
    this.cameraController = null;
    this.lightingManager = null;
    this.pipeline = null;
    this.controlManager = null;
    this.animationLoop = null;
    this.modelLoaderManager = null;
    this.screensaver = null;
    this.objectSelection = null;
    this.objectDoubleClickHandler = null;
    this.uSpaceTooltipManager = null;
  }

  public initialize(container: HTMLElement, buildingData: BuildingData): Promise<void> {
    if (this.initialized) {
      console.warn('Scene already initialized');
      return Promise.resolve();
    }

    this.container = container;
    this.containerRef.value = container;
    const globalThreeStore = useGlobalThreeStore();
    globalThreeStore.setContainerRef(this.containerRef);
    globalThreeStore.resetLoadingState();

    globalThreeStore.setAppReady(false);
    globalThreeStore.setLoadingComplete(false);

    this.sceneManager = SceneManager.getInstance();
    this.cameraController = CameraController.getInstance();
    this.lightingManager = new LightingManager(this.sceneManager.scene);
    this.pipeline = RenderingPipeline.getInstance();
    this.controlManager = ControlManager.getInstance();
    this.animationLoop = AnimationLoop.getInstance();
    this.modelLoaderManager = ModelLoaderManager.getInstance();
    this.screensaver = Screensaver.getInstance();
    this.objectSelection = ObjectSelection.getInstance();
    this.uSpaceTooltipManager = USpaceTooltipManager.getInstance();

    const camera = this.cameraController.camera;
    this.objectDoubleClickHandler = new ObjectDoubleClickHandler(container, camera);

    this._setupEventListeners();
    this.animationLoop.start();

    this.initialized = true;

    return this._loadAllModelsWithTracking(buildingData);
  }

  private _loadAllModelsWithTracking(buildingData: BuildingData): Promise<void> {
    const globalThreeStore = useGlobalThreeStore();
    const totalModels = 1 + (buildingData.floors ? buildingData.floors.length : 0);

    console.log(`[SceneController] 计划加载模型总数: ${totalModels} (外景1 + ${buildingData.floors ? buildingData.floors.length : 0}个楼层)`);
    console.log(`[SceneController] 楼层信息:`, buildingData.floors ? buildingData.floors.map((f) => f.id) : '无楼层');

    globalThreeStore.setModelsLoadInfo(totalModels, 0);

    window.addEventListener('silent-model-loading', this._onSilentLoading.bind(this) as EventListener);

    this.modelLoaderManager?.addModelLoadedCallback(() => {
      globalThreeStore.incrementLoadedModels();
      const loadedCount = globalThreeStore.loadedModelsCount;
      const totalCount = globalThreeStore.totalModelsToLoad;

      console.log(`[SceneController] 模型加载进度更新: ${loadedCount}/${totalCount} (${Math.floor((loadedCount / totalCount) * 100)}%)`);

      if (loadedCount >= totalCount) {
        console.log('[SceneController] 所有模型加载完成，设置应用程序可以交互状态');
        globalThreeStore.setLoadingComplete(true);
        globalThreeStore.setAppReady(true);
      }
    });

    return (this.modelLoaderManager?.showBuilding(buildingData.modelPath) || Promise.resolve()).then(() => {
      return Promise.resolve();
    });
  }

  private _onSilentLoading(event: CustomEvent): void {
    if (event.detail) {
      const globalThreeStore = useGlobalThreeStore();
      globalThreeStore.setSilentLoadingProgress(event.detail.progress);
    }
  }

  public dispose(): void {
    if (!this.initialized) return;

    this.animationLoop?.stop();

    const modelLoader = ModelLoaderManager.getInstance();
    modelLoader.resetAnimations();

    this._removeEventListeners();

    this.objectSelection?.dispose();
    this.controlManager?.dispose();
    this.modelLoaderManager?.dispose();
    this.screensaver?.dispose();
    this.uSpaceTooltipManager?.dispose();
    this.lightingManager?.dispose();
    this.pipeline?.dispose();
    this.sceneManager?.disposeScene(this.pipeline?.getRenderer());

    GridFloorManager.getInstance().dispose();

    useGlobalThreeStore().$reset();

    this.initialized = false;
  }

  public setWeather(weatherType: WeatherType): void {
    // 保持默认天气设置，不再根据天气类型变化
    const weatherManager = WeatherManager.getInstance();
    const skyManager = SkyManager.getInstance();

    weatherManager.setWeather('sunny');
    skyManager.updateSkyForWeather('sunny');
    this.lightingManager?.adjustLightingForWeather(weatherType);
  }

  private _setupEventListeners(): void {
    if (this.container) {
      this.container.addEventListener('click', this.handlers.stopScreensaver);
      this.container.addEventListener('click', this.handlers.handleClick);
      this.container.addEventListener('touchstart', this.handlers.stopScreensaver);
      this.container.addEventListener(
        'dblclick',
        this.objectDoubleClickHandler?.handleDoubleClick.bind(this.objectDoubleClickHandler) as EventListener
      );
    }
  }

  private _removeEventListeners(): void {
    if (this.container) {
      this.container.removeEventListener('click', this.handlers.stopScreensaver);
      this.container.removeEventListener('click', this.handlers.handleClick);
      this.container.removeEventListener('touchstart', this.handlers.stopScreensaver);
      this.container.removeEventListener(
        'dblclick',
        this.objectDoubleClickHandler?.handleDoubleClick.bind(this.objectDoubleClickHandler) as EventListener
      );
    }
  }

  private _stopScreensaver(): void {
    this.screensaver?.stopScreensaver();
  }

  private _handleClick(event: MouseEvent): void {
    // 处理机柜U位点击事件
    const heatmapManager = HeatmapManager.getInstance();
    if (heatmapManager && this.cameraController) {
      // 检查是否有CabinetUSpaceVisualizer实例并且处于活动状态
      const visualizer = (heatmapManager as any).cabinetUSpaceVisualizer;
      if (visualizer && visualizer.isVisualizationActive()) {
        visualizer.handleClick(event, this.cameraController.camera);
      }
    }
  }

  public resume(): void {
    this.animationLoop?.start();
  }

  public pause(): void {
    this.animationLoop?.stop();
  }

  public forceRender(): boolean {
    if (this.pipeline) {
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          this.pipeline?.render();
        }, i * 100);
      }
      return true;
    }
    return false;
  }

  public static getInstance(): SceneController {
    if (!SceneController._instance) {
      SceneController._instance = new SceneController();
    }
    return SceneController._instance;
  }
}
