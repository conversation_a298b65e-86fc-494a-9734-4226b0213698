import * as THREE from 'three';
import { getCabinetDevicesByCabinetId, type CabinetDeviceInfo } from '/@/api/asset/cabinet';
import { ModelLoaderManager } from '../load/ModelLoaderManager';

/**
 * U位状态枚举
 */
export enum USpaceStatus {
  OCCUPIED = 'occupied', // 已占用
  AVAILABLE = 'available', // 空闲
  RESERVED = 'reserved', // 预留
  UNKNOWN = 'unknown', // 未知
}

/**
 * U位信息接口
 */
export interface USpaceInfo {
  position: number; // U位位置 (1-42)
  status: USpaceStatus; // U位状态
  device?: CabinetDeviceInfo; // 关联设备信息
  mesh?: THREE.Mesh; // 3D网格对象
}

/**
 * 机柜信息接口
 */
export interface CabinetInfo {
  id: number;
  name: string;
  totalU: number;
  mesh: THREE.Mesh;
  position: THREE.Vector3;
  uSpaces: USpaceInfo[];
}

/**
 * 机柜U位3D可视化管理器
 * 负责将机柜模型拆分为U位格子，实现占用量的3D可视化
 */
export class CabinetUSpaceVisualizer {
  private scene: THREE.Scene;
  private cabinets: Map<number, CabinetInfo> = new Map();
  private uSpaceGroup: THREE.Group = new THREE.Group();
  private materials: Map<USpaceStatus, THREE.MeshStandardMaterial> = new Map();
  private isActive: boolean = false;
  private raycaster: THREE.Raycaster = new THREE.Raycaster();
  private mouse: THREE.Vector2 = new THREE.Vector2();

  // U位几何体配置
  private readonly U_SPACE_HEIGHT = 0.044; // 1U = 44mm
  private readonly U_SPACE_DEPTH = 0.6; // 机柜深度
  private readonly U_SPACE_WIDTH = 0.48; // 机柜宽度
  private readonly SPACING = 0.002; // U位间隔

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.uSpaceGroup.name = 'CabinetUSpaceGroup';
    this.scene.add(this.uSpaceGroup);
    this.initMaterials();
  }

  /**
   * 初始化材质
   */
  private initMaterials(): void {
    // 已占用 - 红色系
    this.materials.set(
      USpaceStatus.OCCUPIED,
      new THREE.MeshStandardMaterial({
        color: 0xff4444,
        transparent: true,
        opacity: 0.8,
        emissive: 0x220000,
        emissiveIntensity: 0.2,
      })
    );

    // 空闲 - 绿色系
    this.materials.set(
      USpaceStatus.AVAILABLE,
      new THREE.MeshStandardMaterial({
        color: 0x44ff44,
        transparent: true,
        opacity: 0.6,
        emissive: 0x002200,
        emissiveIntensity: 0.1,
      })
    );

    // 预留 - 黄色系
    this.materials.set(
      USpaceStatus.RESERVED,
      new THREE.MeshStandardMaterial({
        color: 0xffff44,
        transparent: true,
        opacity: 0.7,
        emissive: 0x222200,
        emissiveIntensity: 0.15,
      })
    );

    // 未知 - 灰色系
    this.materials.set(
      USpaceStatus.UNKNOWN,
      new THREE.MeshStandardMaterial({
        color: 0x888888,
        transparent: true,
        opacity: 0.4,
        emissive: 0x111111,
        emissiveIntensity: 0.05,
      })
    );
  }

  /**
   * 激活U位可视化
   */
  public async activate(): Promise<void> {
    if (this.isActive) return;

    console.log('[CabinetUSpaceVisualizer] 激活机柜U位可视化');

    try {
      // 收集场景中的机柜
      await this.collectCabinets();

      // 为每个机柜创建U位可视化
      for (const cabinet of this.cabinets.values()) {
        await this.createUSpaceVisualization(cabinet);
      }

      this.isActive = true;
      this.uSpaceGroup.visible = true;

      console.log(`[CabinetUSpaceVisualizer] 已激活，处理了 ${this.cabinets.size} 个机柜`);
    } catch (error) {
      console.error('[CabinetUSpaceVisualizer] 激活失败:', error);
      throw error;
    }
  }

  /**
   * 停用U位可视化
   */
  public deactivate(): void {
    if (!this.isActive) return;

    console.log('[CabinetUSpaceVisualizer] 停用机柜U位可视化');

    this.isActive = false;
    this.uSpaceGroup.visible = false;
    this.clearVisualization();
  }

  /**
   * 收集场景中的机柜模型
   */
  private async collectCabinets(): Promise<void> {
    this.cabinets.clear();

    const modelLoader = ModelLoaderManager.getInstance();
    const models = modelLoader.getCurrentModels();

    console.log(`[CabinetUSpaceVisualizer] 开始收集机柜，当前模型数量: ${models.length}`);

    const stats = { totalMeshCount: 0, floorDeviceCount: 0, cabinetCandidateCount: 0 };

    for (const model of models) {
      console.log(`[CabinetUSpaceVisualizer] 检查模型: ${model.name}, 类型: ${model.userData?.type}`);
      this.traverseForCabinets(model, stats);
    }

    console.log(
      `[CabinetUSpaceVisualizer] 收集完成 - 总网格数: ${stats.totalMeshCount}, 楼层设备数: ${stats.floorDeviceCount}, 机柜候选数: ${stats.cabinetCandidateCount}, 最终机柜数: ${this.cabinets.size}`
    );
  }

  /**
   * 遍历模型寻找机柜
   */
  private traverseForCabinets(
    object: THREE.Object3D,
    stats?: { totalMeshCount: number; floorDeviceCount: number; cabinetCandidateCount: number }
  ): void {
    if (stats && (object as THREE.Mesh).isMesh) {
      stats.totalMeshCount++;
    }

    // 检查是否为楼层设备
    const isFloorDevice = /^(([1-5]F|F[1-5]|BF)_)/.test(object.name);
    if (stats && isFloorDevice) {
      stats.floorDeviceCount++;
      console.log(`[CabinetUSpaceVisualizer] 发现楼层设备: ${object.name}`);
    }

    if (this.isCabinetMesh(object)) {
      if (stats) stats.cabinetCandidateCount++;

      const mesh = object as THREE.Mesh;
      const cabinetId = this.extractCabinetId(mesh);

      console.log(`[CabinetUSpaceVisualizer] 发现机柜候选: ${mesh.name}, 提取ID: ${cabinetId}`);

      if (cabinetId) {
        const cabinet: CabinetInfo = {
          id: cabinetId,
          name: mesh.name,
          totalU: this.estimateTotalU(mesh),
          mesh: mesh,
          position: mesh.position.clone(),
          uSpaces: [],
        };

        this.cabinets.set(cabinetId, cabinet);
        console.log(`[CabinetUSpaceVisualizer] 成功添加机柜: ${cabinet.name}, ID: ${cabinet.id}, 总U位: ${cabinet.totalU}`);
      }
    }

    object.children.forEach((child) => this.traverseForCabinets(child, stats));
  }

  /**
   * 判断是否为机柜网格
   */
  private isCabinetMesh(object: THREE.Object3D): boolean {
    if (!(object as THREE.Mesh).isMesh) return false;

    const name = object.name.toLowerCase();

    // 检查是否为楼层设备（以楼层标识开头）
    const isFloorDevice = /^(([1-5]F|F[1-5]|BF)_)/.test(object.name);

    // 检查是否包含机柜标识
    const isCabinet =
      name.includes('jg') || // 机柜拼音缩写
      name.includes('机柜') ||
      name.includes('cabinet') ||
      name.includes('rack') ||
      name.includes('服务器柜') ||
      name.includes('网络柜');

    return isFloorDevice && isCabinet;
  }

  /**
   * 从网格名称提取机柜ID
   */
  private extractCabinetId(mesh: THREE.Mesh): number | null {
    // 尝试从网格的userData中获取ID
    if (mesh.userData && mesh.userData.cabinetId) {
      return parseInt(mesh.userData.cabinetId);
    }

    // 尝试从名称中提取ID
    // 支持多种命名格式：
    // 1F_jg_123, 2F-jg-456, F1_机柜_789, cabinet_001等
    const patterns = [
      /jg[_-]?(\d+)/i, // jg_123, jg-456
      /机柜[_-]?(\d+)/i, // 机柜_123
      /cabinet[_-]?(\d+)/i, // cabinet_123
      /rack[_-]?(\d+)/i, // rack_123
      /([1-5]F|F[1-5]|BF)_.*?(\d+)$/i, // 楼层标识后的最后一个数字
    ];

    for (const pattern of patterns) {
      const match = mesh.name.match(pattern);
      if (match) {
        const id = parseInt(match[match.length - 1]); // 取最后一个捕获组
        if (!isNaN(id) && id > 0) {
          return id;
        }
      }
    }

    // 如果没有找到明确的ID，使用对象的UUID生成一个数字ID
    const uuidHash = mesh.uuid.replace(/[^0-9]/g, '').slice(0, 6);
    return uuidHash ? (parseInt(uuidHash) % 9999) + 1 : Math.floor(Math.random() * 9999) + 1;
  }

  /**
   * 估算机柜总U位数
   */
  private estimateTotalU(mesh: THREE.Mesh): number {
    // 获取机柜的边界框
    const box = new THREE.Box3().setFromObject(mesh);
    const height = box.max.y - box.min.y;

    // 根据高度估算U位数（1U = 44mm = 0.044m）
    const estimatedU = Math.round(height / this.U_SPACE_HEIGHT);

    // 限制在合理范围内 (通常机柜为42U或更少)
    return Math.min(Math.max(estimatedU, 20), 50);
  }

  /**
   * 为机柜创建U位可视化
   */
  private async createUSpaceVisualization(cabinet: CabinetInfo): Promise<void> {
    try {
      // 获取机柜设备数据
      const devices = await getCabinetDevicesByCabinetId(cabinet.id);

      // 创建U位信息
      cabinet.uSpaces = this.createUSpaceInfo(cabinet.totalU, devices);

      // 创建3D U位网格
      this.createUSpaceMeshes(cabinet);
    } catch (error) {
      console.warn(`[CabinetUSpaceVisualizer] 无法获取机柜 ${cabinet.id} 的设备数据:`, error);

      // 创建默认的U位信息（全部为未知状态）
      cabinet.uSpaces = this.createDefaultUSpaceInfo(cabinet.totalU);
      this.createUSpaceMeshes(cabinet);
    }
  }

  /**
   * 创建U位信息数组
   */
  private createUSpaceInfo(totalU: number, devices: CabinetDeviceInfo[]): USpaceInfo[] {
    const uSpaces: USpaceInfo[] = [];

    // 初始化所有U位为空闲状态
    for (let i = 1; i <= totalU; i++) {
      uSpaces.push({
        position: i,
        status: USpaceStatus.AVAILABLE,
      });
    }

    // 标记设备占用的U位
    devices.forEach((device) => {
      const startU = device.startU;
      const endU = device.endU || startU + (device.uSize || 1) - 1;

      for (let u = startU; u <= endU && u <= totalU; u++) {
        const uSpace = uSpaces[u - 1];
        if (uSpace) {
          uSpace.status = USpaceStatus.OCCUPIED;
          uSpace.device = device;
        }
      }
    });

    return uSpaces;
  }

  /**
   * 创建默认U位信息（未知状态）
   */
  private createDefaultUSpaceInfo(totalU: number): USpaceInfo[] {
    const uSpaces: USpaceInfo[] = [];

    for (let i = 1; i <= totalU; i++) {
      uSpaces.push({
        position: i,
        status: USpaceStatus.UNKNOWN,
      });
    }

    return uSpaces;
  }

  /**
   * 创建U位3D网格
   */
  private createUSpaceMeshes(cabinet: CabinetInfo): void {
    const geometry = new THREE.BoxGeometry(this.U_SPACE_WIDTH, this.U_SPACE_HEIGHT - this.SPACING, this.U_SPACE_DEPTH);

    cabinet.uSpaces.forEach((uSpace, index) => {
      const material = this.materials.get(uSpace.status)!;
      const mesh = new THREE.Mesh(geometry, material);

      // 计算U位位置（从底部开始，1U在最下方）
      const yOffset = index * this.U_SPACE_HEIGHT + this.U_SPACE_HEIGHT / 2;
      mesh.position.copy(cabinet.position);
      mesh.position.y += yOffset;

      // 设置网格数据
      mesh.userData = {
        type: 'uSpace',
        cabinetId: cabinet.id,
        uPosition: uSpace.position,
        status: uSpace.status,
        device: uSpace.device,
      };

      mesh.name = `USpace_${cabinet.id}_${uSpace.position}`;

      // 保存网格引用
      uSpace.mesh = mesh;

      this.uSpaceGroup.add(mesh);
    });
  }

  /**
   * 清除可视化
   */
  private clearVisualization(): void {
    // 清除所有U位网格
    while (this.uSpaceGroup.children.length > 0) {
      const child = this.uSpaceGroup.children[0];
      this.uSpaceGroup.remove(child);

      if (child instanceof THREE.Mesh) {
        child.geometry.dispose();
        // 材质由材质池管理，不需要单独释放
      }
    }

    // 清除机柜数据
    this.cabinets.clear();
  }

  /**
   * 处理鼠标点击事件
   */
  public handleClick(event: MouseEvent, camera: THREE.Camera): void {
    if (!this.isActive) return;

    // 更新鼠标位置
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, camera);
    const intersects = this.raycaster.intersectObjects(this.uSpaceGroup.children);

    if (intersects.length > 0) {
      const mesh = intersects[0].object as THREE.Mesh;
      const userData = mesh.userData;

      if (userData.type === 'uSpace') {
        this.onUSpaceClick(userData);
      }
    }
  }

  /**
   * U位点击处理
   */
  private onUSpaceClick(userData: any): void {
    console.log('[CabinetUSpaceVisualizer] U位点击:', {
      cabinetId: userData.cabinetId,
      uPosition: userData.uPosition,
      status: userData.status,
      device: userData.device,
    });

    // 触发自定义事件，通知其他组件
    window.dispatchEvent(
      new CustomEvent('uspace-clicked', {
        detail: {
          cabinetId: userData.cabinetId,
          uPosition: userData.uPosition,
          status: userData.status,
          device: userData.device,
        },
      })
    );
  }

  /**
   * 获取当前状态
   */
  public isVisualizationActive(): boolean {
    return this.isActive;
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.deactivate();

    // 释放材质
    this.materials.forEach((material) => material.dispose());
    this.materials.clear();

    // 移除组
    this.scene.remove(this.uSpaceGroup);
  }
}
