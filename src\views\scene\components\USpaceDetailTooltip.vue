<template>
  <div
    v-if="visible"
    class="fixed z-[9999] bg-[rgba(15,25,45,0.95)] border border-[rgba(59,142,230,0.3)] rounded-lg p-[1vw] min-w-[15vw] max-w-[25vw] backdrop-blur-sm shadow-lg"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
  >
    <!-- 标题栏 -->
    <div class="flex items-center justify-between mb-[0.8vw]">
      <div class="flex items-center">
        <div class="w-[2vw] h-[2vw] rounded-full bg-gradient-to-br from-blue-500/20 to-blue-600/30 border border-blue-500/30 flex items-center justify-center mr-[0.6vw]">
          <AppstoreOutlined class="text-[1vw] text-blue-400" />
        </div>
        <div>
          <div class="text-[0.9vw] text-white font-medium">{{ cabinetName }}</div>
          <div class="text-[0.7vw] text-gray-400">U位 {{ uPosition }}</div>
        </div>
      </div>
      <button
        @click="close"
        class="w-[1.2vw] h-[1.2vw] rounded-full bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 flex items-center justify-center transition-all"
      >
        <CloseOutlined class="text-[0.6vw] text-red-400" />
      </button>
    </div>

    <!-- U位状态 -->
    <div class="mb-[0.8vw]">
      <div class="flex items-center mb-[0.4vw]">
        <span class="text-[0.7vw] text-gray-400 mr-[0.4vw]">状态：</span>
        <div
          class="px-[0.4vw] py-[0.2vw] rounded text-[0.6vw] font-medium"
          :class="getStatusClass(status)"
        >
          {{ getStatusText(status) }}
        </div>
      </div>
    </div>

    <!-- 设备信息 -->
    <div v-if="device" class="border-t border-gray-600/30 pt-[0.8vw]">
      <div class="text-[0.8vw] text-white font-medium mb-[0.6vw]">设备信息</div>
      
      <div class="space-y-[0.4vw]">
        <div class="flex justify-between">
          <span class="text-[0.7vw] text-gray-400">设备名称：</span>
          <span class="text-[0.7vw] text-white">{{ device.deviceName }}</span>
        </div>
        
        <div v-if="device.deviceType" class="flex justify-between">
          <span class="text-[0.7vw] text-gray-400">设备类型：</span>
          <span class="text-[0.7vw] text-white">{{ device.deviceType }}</span>
        </div>
        
        <div v-if="device.models" class="flex justify-between">
          <span class="text-[0.7vw] text-gray-400">设备型号：</span>
          <span class="text-[0.7vw] text-white">{{ device.models }}</span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-[0.7vw] text-gray-400">起始U位：</span>
          <span class="text-[0.7vw] text-white">{{ device.startU }}</span>
        </div>
        
        <div v-if="device.endU" class="flex justify-between">
          <span class="text-[0.7vw] text-gray-400">结束U位：</span>
          <span class="text-[0.7vw] text-white">{{ device.endU }}</span>
        </div>
        
        <div v-if="device.uSize" class="flex justify-between">
          <span class="text-[0.7vw] text-gray-400">占用U数：</span>
          <span class="text-[0.7vw] text-white">{{ device.uSize }}U</span>
        </div>
      </div>
    </div>

    <!-- 空闲状态提示 -->
    <div v-else-if="status === 'available'" class="border-t border-gray-600/30 pt-[0.8vw]">
      <div class="text-[0.7vw] text-green-400 text-center">
        <CheckCircleOutlined class="mr-[0.2vw]" />
        此U位空闲，可用于设备安装
      </div>
    </div>

    <!-- 预留状态提示 -->
    <div v-else-if="status === 'reserved'" class="border-t border-gray-600/30 pt-[0.8vw]">
      <div class="text-[0.7vw] text-yellow-400 text-center">
        <ExclamationCircleOutlined class="mr-[0.2vw]" />
        此U位已预留，请联系管理员
      </div>
    </div>

    <!-- 未知状态提示 -->
    <div v-else-if="status === 'unknown'" class="border-t border-gray-600/30 pt-[0.8vw]">
      <div class="text-[0.7vw] text-gray-400 text-center">
        <QuestionCircleOutlined class="mr-[0.2vw]" />
        U位状态未知，请检查数据
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="device" class="border-t border-gray-600/30 pt-[0.8vw] mt-[0.8vw]">
      <div class="flex gap-[0.4vw]">
        <button
          @click="viewDeviceDetail"
          class="flex-1 px-[0.6vw] py-[0.3vw] bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded text-[0.6vw] text-blue-400 transition-all"
        >
          查看详情
        </button>
        <button
          @click="unbindDevice"
          class="flex-1 px-[0.6vw] py-[0.3vw] bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded text-[0.6vw] text-red-400 transition-all"
        >
          解绑设备
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { 
    AppstoreOutlined, 
    CloseOutlined, 
    CheckCircleOutlined, 
    ExclamationCircleOutlined, 
    QuestionCircleOutlined 
  } from '@ant-design/icons-vue';
  import type { CabinetDeviceInfo } from '/@/api/asset/cabinet';

  interface Props {
    visible: boolean;
    position: { x: number; y: number };
    cabinetId: number;
    cabinetName: string;
    uPosition: number;
    status: 'occupied' | 'available' | 'reserved' | 'unknown';
    device?: CabinetDeviceInfo;
  }

  interface Emits {
    (e: 'close'): void;
    (e: 'view-device', device: CabinetDeviceInfo): void;
    (e: 'unbind-device', device: CabinetDeviceInfo): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  /**
   * 获取状态样式类
   */
  const getStatusClass = (status: string): string => {
    switch (status) {
      case 'occupied':
        return 'bg-red-500/20 text-red-400 border border-red-500/30';
      case 'available':
        return 'bg-green-500/20 text-green-400 border border-green-500/30';
      case 'reserved':
        return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30';
      case 'unknown':
      default:
        return 'bg-gray-500/20 text-gray-400 border border-gray-500/30';
    }
  };

  /**
   * 获取状态文本
   */
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'occupied':
        return '已占用';
      case 'available':
        return '空闲';
      case 'reserved':
        return '预留';
      case 'unknown':
      default:
        return '未知';
    }
  };

  /**
   * 关闭弹窗
   */
  const close = (): void => {
    emit('close');
  };

  /**
   * 查看设备详情
   */
  const viewDeviceDetail = (): void => {
    if (props.device) {
      emit('view-device', props.device);
    }
  };

  /**
   * 解绑设备
   */
  const unbindDevice = (): void => {
    if (props.device) {
      emit('unbind-device', props.device);
    }
  };
</script>

<style scoped>
/* 确保弹窗在最顶层 */
.fixed {
  pointer-events: auto;
}

/* 动画效果 */
.fixed {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
